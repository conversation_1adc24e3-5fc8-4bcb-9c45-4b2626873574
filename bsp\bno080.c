#include "bno080.h"
#include "bsp_gpio.h"
#include "bsp_uart6.h"
#include <string.h>

// DMA缓冲区和BNO080数据在此处定义实体
uint8_t g_usart6_dma_rx_buf[BNO080_RX_BUFFER_SIZE];
static BNO080_Data_t g_bno080_data = {0};

#define RVC_PACKET_SIZE 19
#define RVC_HEADER      0xAAAA

void BNO080_Init(void) {
    BNO080_GPIO_Init();
    uart6_init(115200);
}

void BNO080_GetData(BNO080_Data_t* data_ptr) {
    __disable_irq();
    memcpy(data_ptr, &g_bno080_data, sizeof(BNO080_Data_t));
    __enable_irq();
}

void BNO080_UART6_IRQHandler(void) {
    if(USART_GetITStatus(USART6, USART_IT_IDLE) != RESET) {
        (void)USART6->SR;
        (void)USART6->DR;
        DMA_Cmd(DMA2_Stream1, DISABLE);

        uint16_t len = BNO080_RX_BUFFER_SIZE - DMA_GetCurrDataCounter(DMA2_Stream1);

        for(uint16_t i = 0; i <= (len >= RVC_PACKET_SIZE ? len - RVC_PACKET_SIZE : 0); ++i) {
            if (*(uint16_t*)(g_usart6_dma_rx_buf + i) == RVC_HEADER) {
                uint8_t checksum = 0;
                for(int j = 2; j < RVC_PACKET_SIZE - 1; ++j) {
                    checksum += g_usart6_dma_rx_buf[i + j];
                }
                if (checksum == g_usart6_dma_rx_buf[i + RVC_PACKET_SIZE - 1]) {
                    int16_t temp;
                    // 安全地从字节数组中提取16位有符号整数
                    memcpy(&temp, &g_usart6_dma_rx_buf[i + 3], 2); g_bno080_data.yaw = temp * 0.01f;
                    memcpy(&temp, &g_usart6_dma_rx_buf[i + 5], 2); g_bno080_data.pitch = temp * 0.01f;
                    memcpy(&temp, &g_usart6_dma_rx_buf[i + 7], 2); g_bno080_data.roll = temp * 0.01f;
                    g_bno080_data.new_data_flag = 1;
                    break; 
                }
            }
        }
        DMA_SetCurrDataCounter(DMA2_Stream1, BNO080_RX_BUFFER_SIZE);
        DMA_Cmd(DMA2_Stream1, ENABLE);
    }
}















